.scheduling-container {
  padding: 16px;
  max-width: 1400px;
  margin: 0 auto;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 16px;
}

.time-display {
  text-align: center;
  margin: 20px 0;
}

.time-display ion-chip {
  font-size: 1.2rem;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scheduling-container {
    padding: 12px;
  }
  
  .control-buttons {
    justify-content: center;
  }
  
  .control-buttons ion-button {
    flex: 1;
    min-width: 120px;
  }
}

/* 进程状态颜色 */
.process-waiting {
  background-color: var(--ion-color-medium);
  color: white;
}

.process-running {
  background-color: var(--ion-color-success);
  color: white;
  animation: pulse 1.5s infinite;
}

.process-completed {
  background-color: var(--ion-color-primary);
  color: white;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 队列可视化样式 */
.queue-container {
  margin: 20px 0;
}

.queue-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--ion-color-primary);
}

.queue-items {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  min-height: 60px;
  padding: 12px;
  border: 2px dashed var(--ion-color-light);
  border-radius: 8px;
  align-items: center;
}

.queue-empty {
  color: var(--ion-color-medium);
  font-style: italic;
  text-align: center;
  width: 100%;
}

.process-item {
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 80px;
  text-align: center;
}

.process-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* CPU 可视化 */
.cpu-container {
  text-align: center;
  margin: 20px 0;
}

.cpu-visual {
  display: inline-block;
  width: 120px;
  height: 80px;
  border: 3px solid var(--ion-color-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--ion-color-primary-tint), var(--ion-color-primary));
  color: white;
  font-weight: bold;
  margin: 0 20px;
}

.cpu-idle {
  background: var(--ion-color-light);
  color: var(--ion-color-medium);
  border-color: var(--ion-color-medium);
}

/* 统计表格 */
.statistics-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.statistics-table th,
.statistics-table td {
  padding: 12px;
  text-align: center;
  border-bottom: 1px solid var(--ion-color-light);
}

.statistics-table th {
  background-color: var(--ion-color-light);
  font-weight: 600;
  color: var(--ion-color-dark);
}

.statistics-table tr:hover {
  background-color: var(--ion-color-light-tint);
}

/* 甘特图样式 */
.gantt-chart {
  margin: 20px 0;
  overflow-x: auto;
}

.gantt-timeline {
  display: flex;
  min-width: 600px;
  height: 60px;
  border: 1px solid var(--ion-color-light);
  border-radius: 6px;
  overflow: hidden;
}

.gantt-block {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.gantt-block:hover {
  filter: brightness(1.1);
  transform: scaleY(1.05);
}

.gantt-time-labels {
  display: flex;
  min-width: 600px;
  margin-top: 8px;
}

.gantt-time-label {
  flex: 1;
  text-align: center;
  font-size: 0.8rem;
  color: var(--ion-color-medium);
  border-right: 1px solid var(--ion-color-light);
  padding: 4px 0;
}

/* 统计卡片样式 */
.stat-card {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: var(--ion-color-light-tint);
  border: 1px solid var(--ion-color-light);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--ion-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

/* 性能分析样式 */
.performance-analysis {
  background: var(--ion-color-light-tint);
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid var(--ion-color-primary);
}

.analysis-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.analysis-item:last-child {
  margin-bottom: 0;
}
